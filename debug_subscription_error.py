#!/usr/bin/env python3
"""
调试订阅错误
"""
import sys
import os
import base64
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app

def debug_subscription_error():
    """调试订阅错误"""
    app = create_app()
    with app.app_context():
        print('=== 调试订阅错误 ===\n')
        
        # 解码token
        token = 'dXNlcl8x'
        try:
            decoded = base64.b64decode(token).decode()
            print(f'Token: {token}')
            print(f'解码后: {decoded}')
        except Exception as e:
            print(f'Token解码失败: {e}')
            return
        
        # 检查用户是否存在
        if decoded.startswith('user_'):
            user_id = int(decoded.replace('user_', ''))
            print(f'用户ID: {user_id}')
            
            from models import User, Order, OrderStatus, Subscription
            
            # 检查用户
            user = User.query.get(user_id)
            print(f'用户存在: {user is not None}')
            if user:
                print(f'用户信息: {user.username}, {user.email}')
            
            # 检查订单
            orders = Order.query.filter(
                Order.user_id == user_id,
                Order.status == OrderStatus.COMPLETED
            ).all()
            print(f'用户订单数: {len(orders)}')
            
            for order in orders:
                print(f'  订单: {order.order_id}, 状态: {order.status.value}')
                
                # 检查订阅
                subscription = Subscription.query.filter_by(order_id=order.id).first()
                if subscription:
                    print(f'    订阅: {subscription.id}, 活跃: {subscription.is_active}')
                else:
                    print(f'    无订阅记录')
                
                # 检查节点配置
                print(f'    节点配置数: {len(order.node_configs)}')
                for config in order.node_configs:
                    print(f'      配置: {config.id}, 活跃: {config.is_active}')
        
        # 测试订阅服务
        print('\n测试订阅服务:')
        from services.subscription_service import SubscriptionService
        
        subscription_service = SubscriptionService()
        
        try:
            # 测试获取订阅配置
            encoded_config = subscription_service.get_subscription_configs(token)
            print(f'订阅配置获取: {"成功" if encoded_config else "失败"}')
            if encoded_config:
                print(f'配置长度: {len(encoded_config)} 字符')
        except Exception as e:
            print(f'订阅配置获取异常: {e}')
        
        try:
            # 测试获取用户订阅
            subscriptions = subscription_service.get_user_subscriptions(user_id)
            print(f'用户订阅获取: 成功，数量: {len(subscriptions)}')
            for sub in subscriptions:
                print(f'  订阅: {sub.get("order_id")}, 活跃: {sub.get("is_active")}')
        except Exception as e:
            print(f'用户订阅获取异常: {e}')

def test_subscription_route():
    """测试订阅路由"""
    app = create_app()
    with app.test_client() as client:
        print('\n=== 测试订阅路由 ===\n')
        
        token = 'dXNlcl8x'
        
        # 测试订阅配置端点
        print('1. 测试 /subscription/<token>:')
        response = client.get(f'/subscription/{token}')
        print(f'   状态码: {response.status_code}')
        if response.status_code != 200:
            print(f'   响应: {response.get_data(as_text=True)}')
        
        # 测试订阅信息端点
        print('\n2. 测试 /api/subscription/info/<token>:')
        response = client.get(f'/api/subscription/info/{token}')
        print(f'   状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.get_json()
            print(f'   数据: {data}')
        else:
            print(f'   响应: {response.get_data(as_text=True)}')
        
        # 测试令牌验证端点
        print('\n3. 测试 /api/subscription/validate/<token>:')
        response = client.get(f'/api/subscription/validate/{token}')
        print(f'   状态码: {response.status_code}')
        if response.status_code == 200:
            data = response.get_json()
            print(f'   验证结果: {data}')
        else:
            print(f'   响应: {response.get_data(as_text=True)}')

if __name__ == '__main__':
    debug_subscription_error()
    test_subscription_route()
