{% extends "base.html" %}

{% block title %}流量数据优化{% endblock %}

{% block content %}
<div class="container py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>
                    <i class="bi bi-speedometer2"></i> 流量数据优化
                </h1>
                <div>
                    <button type="button" class="btn btn-info btn-sm" onclick="refreshStats()">
                        <i class="bi bi-arrow-clockwise"></i> 刷新统计
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="previewCleanup()">
                        <i class="bi bi-eye"></i> 预览清理
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="startCleanupProcess()">
                        <i class="bi bi-trash"></i> 开始清理
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">流量数据优化管理</h5>
                </div>
                <div class="card-body">
                    <!-- 优化说明 -->
                    <div class="alert alert-info">
                        <h5><i class="bi bi-info-circle"></i> 流量数据优化说明</h5>
                        <p class="mb-2">
                            <strong>优化原理：</strong>系统每5分钟收集一次流量数据，长期运行会产生大量记录。
                            优化方案保留最近 <span class="badge bg-primary">{{ stats.retention_days if stats.success else 30 }}</span> 天的详细数据，
                            自动清理更早的历史数据。
                        </p>
                        <p class="mb-0">
                            <strong>优化效果：</strong>减少数据库大小，提高查询性能，不影响任何业务功能。
                        </p>
                    </div>

                    <!-- 当前统计 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="total-records">{{ stats.total_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">总流量记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-database" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="recent-records">{{ stats.recent_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">保留记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-check-circle" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="old-records">{{ stats.old_records if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">可清理记录</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-trash" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h4 id="optimization-ratio">{{ stats.optimization_ratio if stats.success else 'N/A' }}</h4>
                                            <p class="mb-0">优化比例</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-percent" style="font-size: 2rem;"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 时间范围信息 -->
                    {% if stats.success %}
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-calendar"></i> 数据时间范围</h5>
                                </div>
                                <div class="card-body">
                                    <p><strong>最早记录：</strong> 
                                        <span id="earliest-record">{{ stats.earliest_record or '无数据' }}</span>
                                    </p>
                                    <p><strong>最新记录：</strong> 
                                        <span id="latest-record">{{ stats.latest_record or '无数据' }}</span>
                                    </p>
                                    <p><strong>清理截止时间：</strong> 
                                        <span id="cutoff-date">{{ stats.cutoff_date or '无数据' }}</span>
                                    </p>
                                    <p class="mb-0"><strong>保留天数：</strong> 
                                        <span id="retention-days" class="badge bg-primary">{{ stats.retention_days }} 天</span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header">
                                    <h5 class="mb-0"><i class="bi bi-pie-chart"></i> 清理预览</h5>
                                </div>
                                <div class="card-body" id="cleanup-preview">
                                    {% if preview.success %}
                                        {% if preview.will_delete_count > 0 %}
                                        <p><strong>将清理记录：</strong> 
                                            <span class="badge bg-warning">{{ preview.will_delete_count }} 条</span>
                                        </p>
                                        <p><strong>释放空间：</strong> 
                                            <span class="badge bg-info">{{ "%.2f"|format(preview.total_mb_to_free) }} MB</span>
                                        </p>
                                        <p class="mb-0 text-success">
                                            <i class="bi bi-check"></i> {{ preview.message }}
                                        </p>
                                        {% else %}
                                        <p class="text-success mb-0">
                                            <i class="bi bi-check-circle"></i> {{ preview.message }}
                                        </p>
                                        {% endif %}
                                    {% else %}
                                    <p class="text-danger mb-0">
                                        <i class="bi bi-exclamation-triangle"></i> 预览失败：{{ preview.message }}
                                    </p>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- 操作日志 -->
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="bi bi-clock-history"></i> 操作日志</h5>
                        </div>
                        <div class="card-body">
                            <div id="operation-log" style="max-height: 300px; overflow-y: auto;">
                                <p class="text-muted">暂无操作记录</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回按钮 -->
    <div class="row mt-4">
        <div class="col-12">
            <a href="{{ url_for('admin.dashboard') }}" class="btn btn-outline-secondary">
                <i class="bi bi-arrow-left"></i> 返回仪表板
            </a>
        </div>
    </div>
</div>

<!-- 确认清理模态框 -->
<div class="modal fade" id="confirmCleanupModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">确认执行流量数据清理</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i>
                    <strong>注意：</strong>此操作将删除超过保留期的流量统计数据，删除后无法恢复。
                </div>
                <p id="cleanup-confirmation-text">确定要执行清理操作吗？</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="confirmCleanup()">确认清理</button>
            </div>
        </div>
    </div>
</div>













{% endblock %}

{% block scripts %}
<script>
// 刷新统计信息
function refreshStats() {
    showLoading('正在刷新统计信息...');

    fetch('/admin/api/traffic-optimization/stats')
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();
            if (data.success) {
                updateStatsDisplay(data.stats);
                addLog('success', '统计信息刷新成功');
            } else {
                addLog('error', '刷新统计信息失败: ' + data.error);
            }
        })
        .catch(error => {
            hideLoading();
            addLog('error', '刷新统计信息失败: ' + error.message);
        });
}

// 预览清理
function previewCleanup() {
    console.log('previewCleanup: 开始');
    showLoading('正在生成清理预览...');
    addLog('info', '开始生成清理预览...');

    fetch('/admin/api/traffic-optimization/preview')
        .then(response => {
            console.log('previewCleanup: 收到响应', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('previewCleanup: 解析数据', data);
            hideLoading();
            if (data.success && data.preview) {
                updatePreviewDisplay(data.preview);
                addLog('info', '✅ 清理预览生成成功');
            } else {
                addLog('error', '❌ 生成清理预览失败: ' + (data.error || data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('previewCleanup: 错误', error);
            hideLoading();
            addLog('error', '❌ 生成清理预览失败: ' + error.message);
        });
}

// 开始清理流程（改进的用户体验）
function startCleanupProcess() {
    console.log('startCleanupProcess: 开始清理流程');
    addLog('info', '🔍 开始检查可清理的数据...');
    showLoading('正在检查可清理的数据...');

    // 先获取预览信息
    fetch('/admin/api/traffic-optimization/preview')
        .then(response => {
            console.log('startCleanupProcess: 收到响应', response.status);
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log('startCleanupProcess: 解析数据', data);
            hideLoading();
            if (data.success && data.preview && data.preview.will_delete_count > 0) {
                const confirmText = `检查完成！发现 ${data.preview.will_delete_count} 条可清理记录，将释放约 ${data.preview.total_mb_to_free.toFixed(2)} MB 空间。`;
                document.getElementById('cleanup-confirmation-text').textContent = confirmText;
                addLog('success', `✅ 检查完成：发现 ${data.preview.will_delete_count} 条可清理记录`);
                new bootstrap.Modal(document.getElementById('confirmCleanupModal')).show();
            } else if (data.success && data.preview && data.preview.will_delete_count === 0) {
                addLog('info', '✅ 检查完成：当前没有需要清理的数据');
            } else {
                addLog('error', '❌ 检查失败: ' + (data.error || data.message || '未知错误'));
            }
        })
        .catch(error => {
            console.error('startCleanupProcess: 错误', error);
            hideLoading();
            addLog('error', '❌ 检查失败: ' + error.message);
        });
}

// 执行清理（保持原有功能用于预览按钮）
function executeCleanup() {
    startCleanupProcess();
}

// 确认清理
function confirmCleanup() {
    const modal = bootstrap.Modal.getInstance(document.getElementById('confirmCleanupModal'));
    modal.hide();

    addLog('info', '🚀 开始执行数据清理...');
    showLoading('正在执行流量数据清理，请稍候...');

    fetch('/admin/api/traffic-optimization/cleanup', {
        method: 'POST'
    })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            hideLoading();
            if (data.success) {
                addLog('success', `✅ 清理完成：${data.message}`);
                // 显示详细结果
                if (data.deleted_count) {
                    addLog('info', `📊 清理统计：删除了 ${data.deleted_count} 条记录`);
                }
                if (data.total_mb_freed) {
                    addLog('info', `💾 释放空间：${data.total_mb_freed.toFixed(2)} MB`);
                }
                // 刷新统计信息
                addLog('info', '🔄 正在刷新统计信息...');
                setTimeout(refreshStats, 1000);
            } else {
                addLog('error', '❌ 清理失败: ' + data.message);
            }
        })
        .catch(error => {
            hideLoading();
            addLog('error', '❌ 清理失败: ' + error.message);
        });
}

// 更新统计显示
function updateStatsDisplay(stats) {
    if (stats.success) {
        document.getElementById('total-records').textContent = stats.total_records;
        document.getElementById('recent-records').textContent = stats.recent_records;
        document.getElementById('old-records').textContent = stats.old_records;
        document.getElementById('optimization-ratio').textContent = stats.optimization_ratio;
        document.getElementById('earliest-record').textContent = stats.earliest_record || '无数据';
        document.getElementById('latest-record').textContent = stats.latest_record || '无数据';
        document.getElementById('cutoff-date').textContent = stats.cutoff_date || '无数据';
        document.getElementById('retention-days').textContent = stats.retention_days + ' 天';
    }
}

// 更新预览显示
function updatePreviewDisplay(preview) {
    const previewDiv = document.getElementById('cleanup-preview');
    if (!previewDiv) {
        console.error('cleanup-preview element not found');
        return;
    }

    if (preview && preview.success) {
        if (preview.will_delete_count > 0) {
            previewDiv.innerHTML = `
                <p><strong>将清理记录：</strong>
                    <span class="badge bg-warning">${preview.will_delete_count} 条</span>
                </p>
                <p><strong>释放空间：</strong>
                    <span class="badge bg-info">${(preview.total_mb_to_free || 0).toFixed(2)} MB</span>
                </p>
                <p class="mb-0 text-success">
                    <i class="bi bi-check"></i> ${preview.message || '清理预览完成'}
                </p>
            `;
        } else {
            previewDiv.innerHTML = `
                <p class="text-success mb-0">
                    <i class="bi bi-check-circle"></i> ${preview.message || '没有需要清理的数据'}
                </p>
            `;
        }
    } else {
        previewDiv.innerHTML = `
            <p class="text-danger mb-0">
                <i class="bi bi-exclamation-triangle"></i> 预览失败：${preview ? preview.message || '未知错误' : '数据格式错误'}
            </p>
        `;
    }
}

// 添加日志
function addLog(type, message) {
    const logDiv = document.getElementById('operation-log');
    const timestamp = new Date().toLocaleString();
    const iconClass = type === 'success' ? 'bi-check-circle text-success' :
                     type === 'error' ? 'bi-exclamation-circle text-danger' :
                     'bi-info-circle text-info';

    const logEntry = document.createElement('div');
    logEntry.className = 'mb-2';
    logEntry.innerHTML = `
        <small class="text-muted">${timestamp}</small>
        <i class="bi ${iconClass} ms-2"></i>
        <span class="ms-1">${message}</span>
    `;

    // 如果是第一条日志，清除"暂无操作记录"
    if (logDiv.querySelector('.text-muted') && logDiv.children.length === 1) {
        logDiv.innerHTML = '';
    }

    logDiv.insertBefore(logEntry, logDiv.firstChild);

    // 限制日志条数
    while (logDiv.children.length > 20) {
        logDiv.removeChild(logDiv.lastChild);
    }
}

// 显示加载状态
function showLoading(message) {
    console.log('showLoading:', message);

    // 清除之前的超时定时器
    if (window.loadingTimeout) {
        clearTimeout(window.loadingTimeout);
    }

    // 先清除所有现有的加载指示器
    const existingLoaders = document.querySelectorAll('#loading-indicator, [id^="loading-indicator"]');
    console.log(`showLoading: 清除 ${existingLoaders.length} 个现有加载指示器`);
    existingLoaders.forEach(loader => loader.remove());

    // 创建新的加载提示
    console.log('showLoading: 创建新的加载指示器');
    const loadingDiv = document.createElement('div');
    loadingDiv.id = 'loading-indicator';
    loadingDiv.className = 'alert alert-info d-flex align-items-center';
    loadingDiv.style.display = 'flex';
    loadingDiv.style.visibility = 'visible';
    loadingDiv.innerHTML = `
        <div class="spinner-border spinner-border-sm me-2" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <span id="loading-message">${message}</span>
    `;

    // 插入到页面顶部
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(loadingDiv, container.firstChild);
        console.log('showLoading: 加载指示器已插入页面');
        console.log(`showLoading: 元素display: ${loadingDiv.style.display}, visibility: ${loadingDiv.style.visibility}`);
    } else {
        console.error('showLoading: 找不到容器元素');
    }

    // 设置30秒超时自动隐藏（安全措施）
    window.loadingTimeout = setTimeout(() => {
        console.warn('showLoading: 30秒超时，自动隐藏加载状态');
        hideLoading();
        if (typeof addLog === 'function') {
            addLog('warning', '操作超时，已自动停止加载状态');
        }
    }, 30000);
}

// 隐藏加载状态
function hideLoading() {
    console.log('hideLoading: 开始隐藏加载状态');

    // 清除超时定时器
    if (window.loadingTimeout) {
        clearTimeout(window.loadingTimeout);
        window.loadingTimeout = null;
        console.log('hideLoading: 已清除超时定时器');
    }

    // 查找所有可能的加载指示器元素
    const loadingDivs = document.querySelectorAll('#loading-indicator, [id^="loading-indicator"]');
    console.log(`hideLoading: 找到 ${loadingDivs.length} 个加载指示器元素`);

    if (loadingDivs.length > 0) {
        loadingDivs.forEach((div, index) => {
            console.log(`hideLoading: 处理第 ${index + 1} 个元素，当前display: ${div.style.display}`);
            // 使用多种方式确保隐藏
            div.style.display = 'none';
            div.style.visibility = 'hidden';
            div.classList.add('d-none');
            console.log(`hideLoading: 第 ${index + 1} 个元素已隐藏`);
        });
        console.log('hideLoading: 所有加载状态已隐藏');
    } else {
        console.log('hideLoading: 找不到任何加载指示器');
        // 尝试查找可能的其他加载元素
        const allAlerts = document.querySelectorAll('.alert-info');
        console.log(`hideLoading: 找到 ${allAlerts.length} 个alert-info元素`);
        allAlerts.forEach((alert, index) => {
            if (alert.textContent.includes('正在') || alert.textContent.includes('Loading')) {
                console.log(`hideLoading: 隐藏可能的加载元素 ${index + 1}`);
                alert.style.display = 'none';
            }
        });
    }
}

// 页面加载完成后自动刷新一次统计
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded: 页面加载完成');
    // 检查统计数据是否加载成功，如果失败则自动刷新
    const totalRecords = document.getElementById('total-records');
    if (totalRecords && totalRecords.textContent === 'N/A') {
        console.log('DOMContentLoaded: 统计数据加载失败，1秒后自动刷新');
        setTimeout(refreshStats, 1000);
    } else {
        console.log('DOMContentLoaded: 统计数据正常');
    }
});
</script>
{% endblock %}
