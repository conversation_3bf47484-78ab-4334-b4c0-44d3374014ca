#!/usr/bin/env python3
"""
诊断和修复代理连接错误
分析用户报告的HTTPConnectionPool代理连接问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, XUIPanel
from xui_client import XUIClient
import requests
import json

def analyze_error_message():
    """分析错误信息"""
    print("=" * 80)
    print("错误信息分析")
    print("=" * 80)
    
    error_info = {
        "错误类型": "HTTPConnectionPool代理连接错误",
        "具体错误": "ProxyError('Unable to connect to proxy', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。'))",
        "代理地址": "127.0.0.1:10809",
        "目标地址1": "http://*************:5432/tizzyt/panel/inbound/list",
        "目标地址2": "http://************:54321/tizzyt/panel/inbound/list",
        "影响功能": "流量统计收集任务"
    }
    
    print("\n🔍 错误详情:")
    for key, value in error_info.items():
        print(f"   {key}: {value}")
    
    print("\n📋 问题分析:")
    print("   1. 系统尝试通过代理 127.0.0.1:10809 连接X-UI面板")
    print("   2. 代理连接被远程主机强制关闭（错误码10054）")
    print("   3. 导致无法获取面板的入站规则和流量数据")
    print("   4. 流量统计收集任务部分失败")

def check_proxy_configuration():
    """检查代理配置"""
    print("\n" + "=" * 80)
    print("检查代理配置")
    print("=" * 80)
    
    print("\n🔧 检查环境变量代理设置:")
    proxy_vars = ['HTTP_PROXY', 'HTTPS_PROXY', 'http_proxy', 'https_proxy', 'ALL_PROXY']
    
    for var in proxy_vars:
        value = os.environ.get(var)
        if value:
            print(f"   ✅ {var}: {value}")
        else:
            print(f"   ❌ {var}: 未设置")
    
    print("\n🔧 检查requests库代理设置:")
    try:
        # 检查requests的默认代理设置
        session = requests.Session()
        proxies = session.proxies
        if proxies:
            print(f"   ✅ requests默认代理: {proxies}")
        else:
            print("   ❌ requests未设置代理")
            
        # 检查系统代理设置
        import urllib.request
        proxy_handler = urllib.request.getproxies()
        if proxy_handler:
            print(f"   ✅ 系统代理设置: {proxy_handler}")
        else:
            print("   ❌ 系统未设置代理")
            
    except Exception as e:
        print(f"   ❌ 检查代理设置失败: {e}")

def test_direct_connection():
    """测试直接连接（不使用代理）"""
    print("\n" + "=" * 80)
    print("测试直接连接")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 获取面板配置
            panels = XUIPanel.query.all()
            
            if not panels:
                print("   ❌ 数据库中没有找到X-UI面板配置")
                return
            
            print(f"\n📊 找到 {len(panels)} 个面板配置:")
            
            for panel in panels:
                print(f"\n🔗 测试面板: {panel.name}")
                print(f"   地址: {panel.base_url}")
                print(f"   状态: {panel.status}")
                
                # 测试直接连接（禁用代理）
                try:
                    session = requests.Session()
                    session.proxies = {}  # 禁用代理
                    session.timeout = 10
                    
                    # 测试基本连接
                    test_url = f"{panel.base_url}/login"
                    response = session.get(test_url)
                    
                    if response.status_code == 200:
                        print(f"   ✅ 直接连接成功 (状态码: {response.status_code})")
                    else:
                        print(f"   ⚠️  连接响应异常 (状态码: {response.status_code})")
                        
                except requests.exceptions.ProxyError as e:
                    print(f"   ❌ 仍然有代理错误: {e}")
                except requests.exceptions.ConnectionError as e:
                    print(f"   ❌ 连接错误: {e}")
                except requests.exceptions.Timeout as e:
                    print(f"   ❌ 连接超时: {e}")
                except Exception as e:
                    print(f"   ❌ 其他错误: {e}")
                    
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def generate_fix_recommendations():
    """生成修复建议"""
    print("\n" + "=" * 80)
    print("修复建议")
    print("=" * 80)
    
    print("\n🔧 立即修复方案:")
    print("   1. 禁用代理设置")
    print("      - 检查并清除环境变量中的代理设置")
    print("      - 在XUIClient中明确禁用代理")
    print("      - 修改requests.Session配置")
    
    print("\n   2. 修改XUIClient代码")
    print("      - 在__init__方法中设置session.proxies = {}")
    print("      - 添加代理配置选项")
    print("      - 增加连接重试机制")
    
    print("\n   3. 环境配置检查")
    print("      - 确认网络环境是否需要代理")
    print("      - 检查防火墙设置")
    print("      - 验证X-UI面板地址可达性")
    
    print("\n🛠️  代码修复示例:")
    print("""
    # 在XUIClient.__init__中添加:
    self.session = requests.Session()
    self.session.proxies = {}  # 禁用代理
    self.session.timeout = Config.REQUEST_TIMEOUT
    
    # 或者添加代理配置选项:
    def __init__(self, ..., use_proxy=False, proxy_config=None):
        self.session = requests.Session()
        if not use_proxy:
            self.session.proxies = {}
        elif proxy_config:
            self.session.proxies = proxy_config
    """)
    
    print("\n⚡ 临时解决方案:")
    print("   1. 设置环境变量: export NO_PROXY='*'")
    print("   2. 或者: unset HTTP_PROXY HTTPS_PROXY http_proxy https_proxy")
    print("   3. 重启应用服务")

def create_fix_script():
    """创建修复脚本"""
    print("\n" + "=" * 80)
    print("创建修复脚本")
    print("=" * 80)
    
    fix_script = """#!/bin/bash
# X-UI代理连接错误修复脚本

echo "开始修复X-UI代理连接错误..."

# 1. 清除代理环境变量
echo "清除代理环境变量..."
unset HTTP_PROXY
unset HTTPS_PROXY
unset http_proxy
unset https_proxy
unset ALL_PROXY
export NO_PROXY='*'

# 2. 检查当前代理设置
echo "当前代理设置:"
env | grep -i proxy

# 3. 重启应用（如果使用systemd）
# sudo systemctl restart your-app-service

echo "修复完成！请重启应用服务。"
"""
    
    try:
        with open('fix_proxy_error.sh', 'w') as f:
            f.write(fix_script)
        print("   ✅ 修复脚本已创建: fix_proxy_error.sh")
        print("   📋 使用方法: chmod +x fix_proxy_error.sh && ./fix_proxy_error.sh")
    except Exception as e:
        print(f"   ❌ 创建修复脚本失败: {e}")

if __name__ == '__main__':
    print("开始诊断代理连接错误...")
    
    # 分析错误信息
    analyze_error_message()
    
    # 检查代理配置
    check_proxy_configuration()
    
    # 测试直接连接
    test_direct_connection()
    
    # 生成修复建议
    generate_fix_recommendations()
    
    # 创建修复脚本
    create_fix_script()
    
    print(f"\n✅ 诊断完成！")
