#!/usr/bin/env python3
"""
测试流量基准数据处理行为
回答用户关于订阅删除和面板删除时流量基准数据处理的问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from models import db, Subscription, SubscriptionTrafficBaseline, XUIPanel
from services.traffic_baseline_deletion_service import TrafficBaselineDeletionService
from services.panel_traffic_baseline_service import PanelTrafficBaselineService
import json

def test_subscription_deletion_baseline_behavior():
    """测试订阅删除时流量基准的处理行为"""
    print("=" * 80)
    print("测试订阅删除时流量基准数据的处理行为")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 1. 检查当前的删除策略配置
            print("\n1. 当前流量基准删除策略配置:")
            
            deletion_strategy = TrafficBaselineDeletionService.get_strategy_from_config()
            print(f"   删除策略: {deletion_strategy}")
            
            if deletion_strategy == TrafficBaselineDeletionService.DELETION_STRATEGY_HARD:
                print("   ✅ 硬删除模式: 订阅删除时，流量基准数据会被永久删除")
                print("   📝 说明: 流量基准数据不会保留在数据库中")
            else:
                print("   ✅ 软删除模式: 订阅删除时，流量基准数据会被标记为删除但保留在数据库")
                print("   📝 说明: 流量基准数据会保留在数据库中，可以恢复")
            
            # 2. 模拟测试删除行为
            print("\n2. 模拟测试删除行为:")
            
            # 创建测试服务实例
            deletion_service = TrafficBaselineDeletionService(deletion_strategy)
            
            # 查找一个有流量基准的订阅进行测试
            baseline_with_subscription = SubscriptionTrafficBaseline.query.filter_by(is_deleted=False).first()
            
            if baseline_with_subscription:
                subscription_id = baseline_with_subscription.subscription_id
                baseline_mb = round(baseline_with_subscription.baseline_total_bytes / (1024**2), 2)
                
                print(f"   找到测试订阅: {subscription_id}")
                print(f"   当前流量基准: {baseline_mb} MB")
                print(f"   模拟删除策略: {deletion_strategy}")
                
                # 模拟删除处理（不实际执行）
                if deletion_strategy == TrafficBaselineDeletionService.DELETION_STRATEGY_HARD:
                    print("   🔥 硬删除模式: 流量基准数据将被永久删除")
                    print("   ❌ 删除后数据库中不会保留此流量基准记录")
                else:
                    print("   💾 软删除模式: 流量基准数据将被标记为删除")
                    print("   ✅ 删除后数据库中仍会保留此流量基准记录（is_deleted=True）")
            else:
                print("   ℹ️  当前没有活跃的流量基准数据可供测试")
                
        except Exception as e:
            print(f"   ❌ 测试失败: {e}")

def test_panel_deletion_baseline_behavior():
    """测试面板删除时流量基准的处理行为"""
    print("\n" + "=" * 80)
    print("测试面板删除时流量基准数据的处理行为")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            print("\n1. 面板删除时的流量基准处理机制:")
            
            print("   📊 面板删除前的流量上传机制:")
            print("   ✅ 系统会在删除面板前调用 PanelTrafficBaselineService")
            print("   ✅ 从X-UI面板获取所有客户端的当前流量数据")
            print("   ✅ 将流量数据累加到对应订阅的流量基准中")
            print("   ✅ 确保历史流量不会因面板删除而丢失")
            
            print("\n   🔄 具体处理流程:")
            print("   1. 识别受影响的订阅（通过节点配置关联）")
            print("   2. 连接X-UI面板获取实时流量数据")
            print("   3. 为每个订阅创建或更新流量基准记录")
            print("   4. 将面板流量累加到基准中")
            print("   5. 删除面板和相关配置")
            
            # 2. 检查实际的服务实现
            print("\n2. 检查服务实现:")
            
            baseline_service = PanelTrafficBaselineService()
            print("   ✅ PanelTrafficBaselineService 已初始化")
            
            # 查找活跃面板进行说明
            active_panels = XUIPanel.query.filter_by(status='active').all()
            print(f"   📈 当前活跃面板数量: {len(active_panels)}")
            
            if active_panels:
                panel = active_panels[0]
                print(f"   📋 示例面板: {panel.name} ({panel.base_url})")
                print(f"   💡 如果删除此面板，系统会:")
                print(f"      - 连接面板获取所有客户端流量")
                print(f"      - 将流量累加到相关订阅的基准中")
                print(f"      - 确保流量数据不丢失")
            
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")

def check_current_baseline_data():
    """检查当前数据库中的流量基准数据"""
    print("\n" + "=" * 80)
    print("检查当前数据库中的流量基准数据")
    print("=" * 80)
    
    app = create_app()
    
    with app.app_context():
        try:
            # 统计流量基准数据
            total_baselines = SubscriptionTrafficBaseline.query.count()
            active_baselines = SubscriptionTrafficBaseline.query.filter_by(is_deleted=False).count()
            deleted_baselines = SubscriptionTrafficBaseline.query.filter_by(is_deleted=True).count()
            
            print(f"\n📊 流量基准数据统计:")
            print(f"   总记录数: {total_baselines}")
            print(f"   活跃记录: {active_baselines}")
            print(f"   已删除记录: {deleted_baselines}")
            
            if active_baselines > 0:
                print(f"\n📋 活跃流量基准详情:")
                active_records = SubscriptionTrafficBaseline.query.filter_by(is_deleted=False).limit(5).all()
                for baseline in active_records:
                    mb = round(baseline.baseline_total_bytes / (1024**2), 2)
                    print(f"   订阅 {baseline.subscription_id}: {mb} MB")
            
            if deleted_baselines > 0:
                print(f"\n🗑️  已删除流量基准详情:")
                deleted_records = SubscriptionTrafficBaseline.query.filter_by(is_deleted=True).limit(5).all()
                for baseline in deleted_records:
                    mb = round(baseline.baseline_total_bytes / (1024**2), 2)
                    deleted_time = baseline.deleted_at.strftime('%Y-%m-%d %H:%M:%S') if baseline.deleted_at else 'N/A'
                    print(f"   订阅 {baseline.subscription_id}: {mb} MB (删除时间: {deleted_time})")
                    
        except Exception as e:
            print(f"   ❌ 检查失败: {e}")

def generate_answer_summary():
    """生成问题答案总结"""
    print("\n" + "=" * 80)
    print("问题答案总结")
    print("=" * 80)
    
    print("\n🔍 用户问题1: 删除订阅后对应的流量基准还会存在数据库吗？")
    print("📋 答案:")
    print("   ✅ 取决于配置的删除策略")
    print("   🔥 硬删除模式（当前默认）: 流量基准数据会被永久删除，不会保留在数据库")
    print("   💾 软删除模式: 流量基准数据会被标记删除但保留在数据库，可以恢复")
    print("   ⚙️  当前系统配置为硬删除模式，所以删除订阅后流量基准不会保留")
    
    print("\n🔍 用户问题2: 删除面板的时候会把节点的流量上传到流量基准吗？")
    print("📋 答案:")
    print("   ✅ 是的，系统会在删除面板前自动上传流量到基准")
    print("   📊 具体机制:")
    print("      1. 删除面板前，系统调用 PanelTrafficBaselineService")
    print("      2. 连接X-UI面板获取所有客户端的实时流量数据")
    print("      3. 识别受影响的订阅（通过节点配置关联）")
    print("      4. 将流量数据累加到对应订阅的流量基准中")
    print("      5. 确保历史流量不会因面板删除而丢失")
    print("   🛡️  这个机制保护用户的历史流量数据不会因管理员删除面板而丢失")

if __name__ == '__main__':
    print("开始分析流量基准数据处理行为...")
    
    # 测试订阅删除行为
    test_subscription_deletion_baseline_behavior()
    
    # 测试面板删除行为
    test_panel_deletion_baseline_behavior()
    
    # 检查当前数据
    check_current_baseline_data()
    
    # 生成答案总结
    generate_answer_summary()
    
    print(f"\n✅ 分析完成！")
