"""
协议模板管理路由
"""
from flask import Blueprint, request, render_template, redirect, url_for, flash, jsonify
from models import db, ProtocolTemplate, NodeType
from services.protocol_template_service import ProtocolTemplateService
from functools import wraps
import logging

logger = logging.getLogger(__name__)

protocol_template_bp = Blueprint('protocol_template', __name__)

def admin_required(f):
    """管理员权限装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        from flask import session, redirect, url_for, flash
        from models import User, UserRole

        if 'user_id' not in session:
            flash('请先登录', 'error')
            return redirect(url_for('auth.login'))

        user = User.query.get(session['user_id'])
        if not user or user.role != UserRole.ADMIN:
            flash('需要管理员权限', 'error')
            return redirect(url_for('shop.shop_index'))

        return f(*args, **kwargs)
    return decorated_function

@protocol_template_bp.route('/admin/protocol-templates')
@admin_required
def list_templates():
    """协议模板列表页面"""
    try:
        templates = ProtocolTemplate.query.order_by(ProtocolTemplate.protocol_type, ProtocolTemplate.name).all()
        return render_template('admin/protocol_templates/list.html', templates=templates)
    except Exception as e:
        logger.error(f"获取协议模板列表失败: {e}")
        flash('获取协议模板列表失败', 'error')
        return redirect(url_for('admin.dashboard'))

@protocol_template_bp.route('/admin/protocol-templates/create', methods=['GET', 'POST'])
@admin_required
def create_template():
    """创建协议模板"""
    if request.method == 'GET':
        template_service = ProtocolTemplateService()
        placeholders = template_service.get_available_placeholders()
        return render_template('admin/protocol_templates/create.html', 
                             node_types=NodeType, 
                             placeholders=placeholders)
    
    try:
        # 获取表单数据
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        protocol_type = request.form.get('protocol_type')
        custom_protocol_name = request.form.get('custom_protocol_name', '').strip()
        template_content = request.form.get('template_content', '').strip()
        
        # 验证必需字段
        if not name or not protocol_type or not template_content:
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('protocol_template.create_template'))
        
        # 检查名称是否已存在
        existing = ProtocolTemplate.query.filter_by(name=name).first()
        if existing:
            flash('协议模板名称已存在', 'error')
            return redirect(url_for('protocol_template.create_template'))
        
        # 验证协议类型
        try:
            protocol_type_enum = NodeType(protocol_type)
        except ValueError:
            flash('无效的协议类型', 'error')
            return redirect(url_for('protocol_template.create_template'))

        # 验证自定义协议名称
        if protocol_type_enum == NodeType.CUSTOM and not custom_protocol_name:
            flash('自定义协议类型必须填写协议名称', 'error')
            return redirect(url_for('protocol_template.create_template'))
        
        # 验证模板内容
        template_service = ProtocolTemplateService()
        is_valid, missing_placeholders = template_service.validate_template(template_content, protocol_type_enum)
        
        if not is_valid:
            flash(f'模板验证失败，缺少必需占位符: {", ".join(missing_placeholders)}', 'error')
            return redirect(url_for('protocol_template.create_template'))
        
        # 创建协议模板
        template = ProtocolTemplate(
            name=name,
            description=description,
            protocol_type=protocol_type_enum,
            custom_protocol_name=custom_protocol_name if protocol_type_enum == NodeType.CUSTOM else None,
            template_content=template_content,
            is_active=True,
            is_default=False
        )
        
        db.session.add(template)
        db.session.commit()
        
        flash('协议模板创建成功', 'success')
        return redirect(url_for('protocol_template.list_templates'))
        
    except Exception as e:
        logger.error(f"创建协议模板失败: {e}")
        db.session.rollback()
        flash('创建协议模板失败', 'error')
        return redirect(url_for('protocol_template.create_template'))

@protocol_template_bp.route('/admin/protocol-templates/<int:template_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_template(template_id):
    """编辑协议模板"""
    template = ProtocolTemplate.query.get_or_404(template_id)
    
    if request.method == 'GET':
        template_service = ProtocolTemplateService()
        placeholders = template_service.get_available_placeholders()
        return render_template('admin/protocol_templates/edit.html', 
                             template=template, 
                             node_types=NodeType,
                             placeholders=placeholders)
    
    try:
        # 获取表单数据
        name = request.form.get('name', '').strip()
        description = request.form.get('description', '').strip()
        protocol_type = request.form.get('protocol_type')
        custom_protocol_name = request.form.get('custom_protocol_name', '').strip()
        template_content = request.form.get('template_content', '').strip()
        is_active = request.form.get('is_active') == 'on'
        
        # 验证必需字段
        if not name or not protocol_type or not template_content:
            flash('请填写所有必需字段', 'error')
            return redirect(url_for('protocol_template.edit_template', template_id=template_id))
        
        # 检查名称是否已存在（排除当前模板）
        existing = ProtocolTemplate.query.filter(
            ProtocolTemplate.name == name,
            ProtocolTemplate.id != template_id
        ).first()
        if existing:
            flash('协议模板名称已存在', 'error')
            return redirect(url_for('protocol_template.edit_template', template_id=template_id))
        
        # 验证协议类型
        try:
            protocol_type_enum = NodeType(protocol_type)
        except ValueError:
            flash('无效的协议类型', 'error')
            return redirect(url_for('protocol_template.edit_template', template_id=template_id))

        # 验证自定义协议名称
        if protocol_type_enum == NodeType.CUSTOM and not custom_protocol_name:
            flash('自定义协议类型必须填写协议名称', 'error')
            return redirect(url_for('protocol_template.edit_template', template_id=template_id))
        
        # 验证模板内容
        template_service = ProtocolTemplateService()
        is_valid, missing_placeholders = template_service.validate_template(template_content, protocol_type_enum)
        
        if not is_valid:
            flash(f'模板验证失败，缺少必需占位符: {", ".join(missing_placeholders)}', 'error')
            return redirect(url_for('protocol_template.edit_template', template_id=template_id))
        
        # 更新协议模板
        template.name = name
        template.description = description
        template.protocol_type = protocol_type_enum
        template.custom_protocol_name = custom_protocol_name if protocol_type_enum == NodeType.CUSTOM else None
        template.template_content = template_content
        template.is_active = is_active
        
        db.session.commit()
        
        flash('协议模板更新成功', 'success')
        return redirect(url_for('protocol_template.list_templates'))
        
    except Exception as e:
        logger.error(f"更新协议模板失败: {e}")
        db.session.rollback()
        flash('更新协议模板失败', 'error')
        return redirect(url_for('protocol_template.edit_template', template_id=template_id))

@protocol_template_bp.route('/admin/protocol-templates/<int:template_id>/delete', methods=['POST'])
@admin_required
def delete_template(template_id):
    """删除协议模板"""
    try:
        template = ProtocolTemplate.query.get_or_404(template_id)

        # 检查是否有面板组成员关系在使用此模板
        from models import XUIPanelGroupMembership
        usage_count = XUIPanelGroupMembership.query.filter_by(protocol_template_id=template_id).count()

        if usage_count > 0:
            flash(f'无法删除协议模板，有 {usage_count} 个面板组成员关系正在使用此模板', 'error')
            return redirect(url_for('protocol_template.list_templates'))

        # 记录删除的模板信息
        template_name = template.name
        is_default = template.is_default

        db.session.delete(template)
        db.session.commit()

        if is_default:
            flash(f'默认协议模板 "{template_name}" 删除成功', 'warning')
        else:
            flash(f'协议模板 "{template_name}" 删除成功', 'success')
        
    except Exception as e:
        logger.error(f"删除协议模板失败: {e}")
        db.session.rollback()
        flash('删除协议模板失败', 'error')
    
    return redirect(url_for('protocol_template.list_templates'))

@protocol_template_bp.route('/admin/api/protocol-templates/<int:template_id>/preview', methods=['POST'])
@admin_required
def preview_template(template_id):
    """预览协议模板"""
    try:
        template = ProtocolTemplate.query.get_or_404(template_id)
        
        # 使用示例数据
        sample_variables = {
            'client_id': '12345678-1234-1234-1234-123456789abc',
            'client_email': '<EMAIL>',
            'server_address': 'example.com',
            'server_port': 443,
            'protocol': template.protocol_type.value,
            'network': 'tcp',
            'security': 'tls',
            'server_name': 'example.com',
            'alpn': 'h2,http/1.1',
            'ws_path': '/path',
            'ws_host': 'example.com'
        }
        
        template_service = ProtocolTemplateService()
        preview_config = template_service.parse_template(template.template_content, sample_variables)
        
        return jsonify({
            'success': True,
            'preview': preview_config
        })
        
    except Exception as e:
        logger.error(f"预览协议模板失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@protocol_template_bp.route('/admin/api/protocol-templates/validate', methods=['POST'])
@admin_required
def validate_template():
    """验证协议模板"""
    try:
        data = request.get_json()
        template_content = data.get('template_content', '')
        protocol_type = data.get('protocol_type', '')
        
        if not template_content or not protocol_type:
            return jsonify({
                'success': False,
                'error': '缺少必需参数'
            }), 400
        
        try:
            protocol_type_enum = NodeType(protocol_type)
        except ValueError:
            return jsonify({
                'success': False,
                'error': '无效的协议类型'
            }), 400
        
        template_service = ProtocolTemplateService()
        is_valid, missing_placeholders = template_service.validate_template(template_content, protocol_type_enum)
        
        return jsonify({
            'success': True,
            'is_valid': is_valid,
            'missing_placeholders': missing_placeholders
        })
        
    except Exception as e:
        logger.error(f"验证协议模板失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
